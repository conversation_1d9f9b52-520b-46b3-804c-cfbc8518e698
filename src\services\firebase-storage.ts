import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject,
  getMetadata,
  type UploadResult 
} from 'firebase/storage';
import { storage } from '../config/firebase';

export interface UploadProgress {
  bytesTransferred: number;
  totalBytes: number;
  percentage: number;
}

export interface FileUploadResult {
  downloadURL: string;
  path: string;
  fileName: string;
  size: number;
  mimeType: string;
}

export class FirebaseStorageService {
  /**
   * Upload a file to Firebase Storage
   * @param file - The file to upload
   * @param path - The storage path (e.g., 'images/book1/chapter1/item1')
   * @param onProgress - Optional progress callback
   * @returns Promise with upload result
   */
  static async uploadFile(
    file: File, 
    path: string  ): Promise<FileUploadResult> {
    try {
      // Create a reference to the file location
      const storageRef = ref(storage, path);
      
      // Upload the file
      const uploadResult: UploadResult = await uploadBytes(storageRef, file);
      
      // Get the download URL
      const downloadURL = await getDownloadURL(uploadResult.ref);
      
      // Get file metadata
      const metadata = await getMetadata(uploadResult.ref);
      
      return {
        downloadURL,
        path: uploadResult.ref.fullPath,
        fileName: file.name,
        size: metadata.size,
        mimeType: metadata.contentType || file.type
      };
    } catch (error) {
      console.error('Error uploading file:', error);
      throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete a file from Firebase Storage
   * @param path - The storage path of the file to delete
   */
  static async deleteFile(path: string): Promise<void> {
    try {
      const storageRef = ref(storage, path);
      await deleteObject(storageRef);
      console.log('File deleted successfully:', path);
    } catch (error) {
      console.error('Error deleting file:', error);
      throw new Error(`Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get the download URL for a file
   * @param path - The storage path of the file
   * @returns Promise with the download URL
   */
  static async getDownloadURL(path: string): Promise<string> {
    try {
      const storageRef = ref(storage, path);
      return await getDownloadURL(storageRef);
    } catch (error) {
      console.error('Error getting download URL:', error);
      throw new Error(`Failed to get download URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate a storage path for an item image
   * @param bookId - The book ID
   * @param chapterId - The chapter ID
   * @param itemId - The item ID
   * @param fileName - The original file name
   * @returns The storage path
   */
  static generateImagePath(bookId: string, chapterId: string, itemId: string, fileName: string): string {
    // Extract file extension
    const extension = fileName.split('.').pop() || '';
    // Create a clean filename with timestamp to avoid conflicts
    const timestamp = Date.now();
    const cleanFileName = `${itemId}_${timestamp}.${extension}`;
    
    return `images/${bookId}/${chapterId}/${cleanFileName}`;
  }

  /**
   * Validate file type for image uploads
   * @param file - The file to validate
   * @returns True if valid image file
   */
  static validateImageFile(file: File): { valid: boolean; error?: string } {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Invalid file type. Please upload a JPEG, PNG, GIF, or WebP image.'
      };
    }

    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File size too large. Please upload an image smaller than 10MB.'
      };
    }

    return { valid: true };
  }
}
