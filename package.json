{"name": "textbook-platform", "private": true, "version": "0.3.0", "description": "An online content management platform for textbooks", "author": "<PERSON><PERSON>", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --mode production", "dev:local": "concurrently \"npm run server\" \"npm run frontend:dev\"", "frontend": "vite --host 0.0.0.0", "frontend:dev": "vite --host 0.0.0.0 --mode development", "server": "node server.cjs", "build": "vue-tsc -b && vite build", "build:dev": "vue-tsc -b && vite build --mode development", "preview": "vite preview", "check-env": "node scripts/check-env.js"}, "dependencies": {"@primeuix/themes": "^1.1.2", "@tailwindcss/vite": "^4.1.11", "bidi-js": "^1.0.3", "canvas": "^3.1.2", "cors": "^2.8.5", "express": "^5.1.0", "firebase": "^11.10.0", "firebase-admin": "^13.4.0", "jsdom": "^26.1.0", "multer": "^2.0.1", "pdfkit": "^0.17.1", "pinia": "^3.0.3", "primeicons": "^7.0.0", "primevue": "^4.3.5", "qrcode": "^1.5.4", "sharp": "^0.34.2", "tailwindcss": "^4.1.11", "tailwindcss-primeui": "^0.6.1", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "typescript": "~5.8.3", "vite": "^7.0.0", "vue-tsc": "^3.0.0"}}