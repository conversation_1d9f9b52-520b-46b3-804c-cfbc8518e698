<template>
  <div class="item-preview">
    <!-- Basic Item Info -->
    <div class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-surface-600 mb-1">Type</label>
        <Chip :label="item?.type" class="capitalize" />
      </div>

      <!-- Question Type Preview -->
      <div v-if="item?.type === 'question'">
        <div v-if="item.question">
          <label class="block text-sm font-medium text-surface-600 mb-1">Question</label>
          <p class="text-surface-800 mb-3">{{ item.question }}</p>
        </div>
        <div v-if="item.options && item.options.length > 0">
          <label class="block text-sm font-medium text-surface-600 mb-2">Options</label>
          <div class="space-y-2">
            <div 
              v-for="(option, index) in item.options" 
              :key="index"
              class="flex items-center gap-2 p-2 border border-surface-200 rounded-lg"
              :class="{ 'bg-green-50 border-green-200': index === item.correctAnswer }"
            >
              <span class="font-medium text-sm">{{ String.fromCharCode(65 + index) }}.</span>
              <span class="text-sm">{{ option.text }}</span>
              <Badge v-if="index === item.correctAnswer" value="Correct" severity="success" class="ml-auto" />
            </div>
          </div>
        </div>
      </div>

      <!-- Test Type Preview -->
      <div v-else-if="item?.type === 'test'">
        <div v-if="item.testQuestions && item.testQuestions.length > 0">
          <!-- Test Header with Navigation -->
          <div class="flex items-center justify-between mb-4">
            <label class="block text-sm font-medium text-surface-600">Test Questions</label>
            <div class="flex items-center gap-2">
              <Button 
                icon="pi pi-chevron-left" 
                size="small" 
                outlined 
                @click="previousQuestion"
                :disabled="currentQuestionIndex === 0"
                v-tooltip="'Previous Question'"
              />
              <span class="text-sm font-medium px-2">
                {{ currentQuestionIndex + 1 }}/{{ item.testQuestions.length }}
              </span>
              <Button 
                icon="pi pi-chevron-right" 
                size="small" 
                outlined 
                @click="nextQuestion"
                :disabled="currentQuestionIndex === item.testQuestions.length - 1"
                v-tooltip="'Next Question'"
              />
            </div>
          </div>

          <!-- Current Question Display -->
          <div v-if="currentQuestion" class="border border-surface-200 rounded-lg p-4">
            <h4 class="font-medium text-surface-800 mb-3">
              Q{{ currentQuestionIndex + 1 }}: {{ currentQuestion.question }}
            </h4>
            <div class="space-y-2">
              <div 
                v-for="(option, index) in currentQuestion.options" 
                :key="index"
                class="flex items-center gap-2 p-2 border border-surface-200 rounded-lg"
                :class="{ 'bg-green-50 border-green-200': index === currentQuestion.correctAnswer }"
              >
                <span class="font-medium text-sm">{{ String.fromCharCode(65 + index) }}.</span>
                <span class="text-sm">{{ option.text }}</span>
                <Badge v-if="index === currentQuestion.correctAnswer" value="Correct" severity="success" class="ml-auto" />
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <p class="text-surface-500 text-sm">No test questions available</p>
        </div>
      </div>

      <!-- Text Content Preview -->
      <div v-else-if="item?.type === 'text'">
        <div v-if="item.content">
          <label class="block text-sm font-medium text-surface-600 mb-1">Content</label>
          <div class="text-surface-800 text-sm leading-relaxed">{{ item.content }}</div>
        </div>
      </div>

      <!-- Link Preview -->
      <div v-else-if="item?.type === 'link'">
        <div v-if="item.url">
          <label class="block text-sm font-medium text-surface-600 mb-1">URL</label>
          <a :href="item.url" target="_blank" class="text-primary-600 hover:text-primary-700 text-sm break-all">
            {{ item.url }}
          </a>
        </div>
      </div>

      <!-- Timed Question Preview -->
      <div v-else-if="item?.type === 'timed-question'">
        <div v-if="item.question">
          <label class="block text-sm font-medium text-surface-600 mb-1">Question</label>
          <p class="text-surface-800 mb-3">{{ item.question }}</p>
        </div>
        <div v-if="item.timedAnswer">
          <label class="block text-sm font-medium text-surface-600 mb-1">Answer</label>
          <p class="text-surface-800 mb-3">{{ item.timedAnswer }}</p>
        </div>
        <div v-if="item.revealTimeSeconds">
          <label class="block text-sm font-medium text-surface-600 mb-1">Reveal Time</label>
          <Chip :label="`${item.revealTimeSeconds} seconds`" icon="pi pi-clock" />
        </div>
      </div>

      <!-- Image/Map/Diagram Preview -->
      <div v-else-if="['image', 'map', 'diagram'].includes(item?.type as string)">
        <!-- Show actual image if available -->
        <div v-if="item?.imageUrl" class="space-y-3">
          <div class="border border-surface-200 rounded-lg overflow-hidden">
            <img
              :src="item.imageUrl"
              :alt="item.title"
              class="w-full h-auto max-h-64 object-contain bg-surface-50"
              @error="handleImageError"
            />
          </div>
          <div class="text-xs text-surface-500 space-y-1">
            <div v-if="item.imageFileName">
              <span class="font-medium">File:</span> {{ item.imageFileName }}
            </div>
            <div v-if="item.imageSize">
              <span class="font-medium">Size:</span> {{ formatFileSize(item.imageSize) }}
            </div>
            <div v-if="item.imageMimeType">
              <span class="font-medium">Type:</span> {{ item.imageMimeType }}
            </div>
          </div>
        </div>

        <!-- Fallback when no image is uploaded -->
        <div v-else class="p-4 border-2 border-dashed border-surface-300 rounded-lg text-center">
          <Avatar
            :icon="getTypeIcon(item?.type)"
            size="large"
            class="mb-2 bg-surface-100 text-surface-400"
          />
          <p class="text-sm text-surface-500">No {{ getTypeLabel(item?.type).toLowerCase() }} uploaded</p>
        </div>
      </div>

      <!-- Generic fallback -->
      <div v-else>
        <p class="text-surface-500 text-sm">Preview not available for this item type</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { Item } from '../../types/item';
import Chip from 'primevue/chip';
import Button from 'primevue/button';
import Badge from 'primevue/badge';
import Avatar from 'primevue/avatar';

interface Props {
  item: Item | null;
}

const props = defineProps<Props>();

// Test question navigation
const currentQuestionIndex = ref(0);

const currentQuestion = computed(() => {
  if (!props.item?.testQuestions || props.item.testQuestions.length === 0) {
    return null;
  }
  return props.item.testQuestions[currentQuestionIndex.value];
});

const nextQuestion = () => {
  if (props.item?.testQuestions && currentQuestionIndex.value < props.item.testQuestions.length - 1) {
    currentQuestionIndex.value++;
  }
};

const previousQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--;
  }
};

const handleImageError = (event: Event) => {
  console.error('Error loading image:', event);
  // Could show a placeholder or error message
};

const formatFileSize = (bytes: number | undefined): string => {
  if (!bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const getTypeIcon = (type: string | undefined) => {
  switch (type) {
    case 'image': return 'pi pi-image';
    case 'map': return 'pi pi-map';
    case 'diagram': return 'pi pi-chart-bar';
    default: return 'pi pi-file';
  }
};

const getTypeLabel = (type: string | undefined) => {
  switch (type) {
    case 'image': return 'Image';
    case 'map': return 'Map';
    case 'diagram': return 'Diagram';
    default: return 'File';
  }
};

// Reset question index when item changes
watch(() => props.item, () => {
  currentQuestionIndex.value = 0;
});
</script>
