<template>
  <Dialog
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    :header="dialogTitle"
    :style="{ width: '80vw', maxWidth: '1000px' }"
    :modal="true"
    :closable="true"
    :draggable="false"
    class="item-form-dialog"
    @hide="handleClose"
  >
    <template #header>
      <div class="flex items-center gap-2">
        <Avatar 
          :icon="dialogIcon" 
          size="small" 
          :class="dialogIconClass" 
        />
        <span class="font-semibold">{{ dialogTitle }}</span>
      </div>
    </template>

    <div class="p-4 max-h-[70vh] overflow-y-auto">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Basic Information Section (Left Column - Narrower) -->
        <div class="lg:col-span-1">
          <Panel header="Basic Information" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-info-circle" size="small" class="bg-blue-100 text-blue-600" />
                <span class="font-semibold">Basic Information</span>
              </div>
            </template>
            <div class="space-y-3">
              <div class="space-y-2">
                <label for="itemType" class="block text-sm font-semibold text-surface-700">Item Type</label>
                <Select
                  id="itemType"
                  v-model="item.type"
                  :options="itemTypes"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select Item Type"
                  class="w-full"
                />
              </div>
              
              <div class="space-y-2">
                <label for="itemTitle" class="block text-sm font-semibold text-surface-700">Title</label>
                <InputText
                  id="itemTitle"
                  v-model="item.title"
                  placeholder="Enter item title"
                  class="w-full"
                />
              </div>
            </div>
          </Panel>
        </div>

        <!-- Content Section (Right Column - Wider) -->
        <div class="lg:col-span-2 space-y-3">
          <!-- Question Type Content -->
          <Panel v-if="item.type === 'question'" header="Question Details" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-question-circle" size="small" class="bg-orange-100 text-orange-600" />
                <span class="font-semibold">Question Details</span>
              </div>
            </template>
            <div class="space-y-4">
              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Question Text</label>
                <InputText
                  v-model="item.question"
                  placeholder="Enter your question"
                  class="w-full"
                />
              </div>

              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <label class="block text-sm font-semibold text-surface-700">Answer Options</label>
                  <Button
                    label="Add Option"
                    icon="pi pi-plus"
                    size="small"
                    outlined
                    @click="addOption"
                    class="transition-all duration-200"
                  />
                </div>
                <div class="space-y-4 max-h-74 overflow-y-auto">
                  <div v-for="(option, index) in item.options" :key="index"
                       class="flex items-center gap-3 rounded-lg hover:border-primary-300 transition-colors">
                    <RadioButton
                      :value="index"
                      v-model="item.correctAnswer"
                      :inputId="`option-${index}`"
                    />
                    <InputText
                      v-model="option.text"
                      placeholder="Enter option text"
                      fluid
                    />
                    <Badge
                      v-if="index === item.correctAnswer"
                      value="Correct"
                      severity="success"
                      class="ml-2"
                    />
                    <Button
                      icon="pi pi-trash"
                      size="small"
                      severity="danger"
                      text
                      @click="removeOption(index)"
                      class="transition-all duration-200"
                    />
                  </div>
                </div>
              </div>
            </div>
          </Panel>

          <!-- Test Type Content -->
          <Panel v-else-if="item.type === 'test'" header="Test Details" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-list-check" size="small" class="bg-purple-100 text-purple-600" />
                <span class="font-semibold">Test Details</span>
              </div>
            </template>
            <div class="space-y-4">
              <!-- Test Questions Header with Navigation -->
              <div class="flex items-center justify-between">
                <label class="block text-sm font-semibold text-surface-700">Test Questions</label>
                <div class="flex items-center gap-2">
                  <Button
                    label="Add Question"
                    icon="pi pi-plus"
                    size="small"
                    outlined
                    @click="addTestQuestion"
                    class="transition-all duration-200"
                  />
                  <div v-if="item.testQuestions && item.testQuestions.length > 0" class="flex items-center gap-2 ml-4">
                    <Button
                      icon="pi pi-chevron-left"
                      size="small"
                      outlined
                      @click="previousTestQuestion"
                      :disabled="currentTestQuestionIndex === 0"
                      v-tooltip="'Previous Question'"
                    />
                    <span class="text-sm font-medium px-2">
                      {{ currentTestQuestionIndex + 1 }}/{{ item.testQuestions.length }}
                    </span>
                    <Button
                      icon="pi pi-chevron-right"
                      size="small"
                      outlined
                      @click="nextTestQuestion"
                      :disabled="currentTestQuestionIndex === item.testQuestions.length - 1"
                      v-tooltip="'Next Question'"
                    />
                  </div>
                </div>
              </div>

              <!-- Current Test Question Editor -->
              <div v-if="currentTestQuestion" class="border border-surface-200 rounded-lg p-4 space-y-3">
                <div class="flex items-center justify-between">
                  <h4 class="font-medium text-surface-700">Question {{ currentTestQuestionIndex + 1 }}</h4>
                  <Button
                    icon="pi pi-trash"
                    size="small"
                    severity="danger"
                    text
                    @click="removeTestQuestion(currentTestQuestionIndex)"
                    v-tooltip="'Delete Question'"
                  />
                </div>

                <div class="space-y-2">
                  <label class="text-sm font-medium text-surface-600">Question Text</label>
                  <InputText
                    v-model="currentTestQuestion.question"
                    placeholder="Enter question text"
                    class="w-full"
                  />
                </div>

                <div class="space-y-2">
                  <div class="flex items-center justify-between">
                    <label class="text-sm font-medium text-surface-600">Answer Options</label>
                    <Button
                      label="Add Option"
                      icon="pi pi-plus"
                      size="small"
                      text
                      @click="addTestQuestionOption(currentTestQuestionIndex)"
                    />
                  </div>

                  <div v-for="(option, oIndex) in currentTestQuestion.options" :key="oIndex"
                       class="flex items-center gap-2">
                    <RadioButton
                      :value="oIndex"
                      v-model="currentTestQuestion.correctAnswer"
                      :inputId="`test-q${currentTestQuestionIndex}-option-${oIndex}`"
                    />
                    <InputText
                      v-model="option.text"
                      placeholder="Enter option text"
                      class="flex-1"
                    />
                    <Badge
                      v-if="oIndex === currentTestQuestion.correctAnswer"
                      value="Correct"
                      severity="success"
                      class="ml-2"
                    />
                    <Button
                      icon="pi pi-trash"
                      size="small"
                      severity="danger"
                      text
                      @click="removeTestQuestionOption(currentTestQuestionIndex, oIndex)"
                      :disabled="currentTestQuestion.options.length <= 2"
                      v-tooltip="'Delete Option'"
                    />
                  </div>
                </div>
              </div>

              <!-- No Questions State -->
              <div v-else-if="!item.testQuestions || item.testQuestions.length === 0"
                   class="text-center py-8 border-2 border-dashed border-surface-300 rounded-lg">
                <Avatar icon="pi pi-question-circle" size="large" class="mb-3 bg-surface-100 text-surface-400" />
                <p class="text-surface-500 mb-3">No test questions yet</p>
                <Button
                  label="Add First Question"
                  icon="pi pi-plus"
                  @click="addTestQuestion"
                  outlined
                />
              </div>
            </div>
          </Panel>

          <!-- Timed Question Type Content -->
          <Panel v-else-if="item.type === 'timed-question'" header="Timed Question" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-clock" size="small" class="bg-amber-100 text-amber-600" />
                <span class="font-semibold">Timed Question</span>
              </div>
            </template>
            <div class="space-y-4">
              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Question</label>
                <InputText
                  v-model="item.question"
                  placeholder="Enter your open-ended question"
                  class="w-full"
                />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Predefined Answer</label>
                <Textarea
                  v-model="item.timedAnswer"
                  placeholder="Enter the answer that will be revealed after the timer"
                  rows="4"
                  class="w-full resize-none"
                />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Reveal Time (seconds)</label>
                <InputNumber
                  v-model="item.revealTimeSeconds"
                  placeholder="30"
                  class="w-full"
                  :min="5"
                  :max="300"
                />
              </div>
            </div>
          </Panel>

          <!-- Other content types... -->
          <Panel v-else-if="item.type === 'text'" header="Text Content" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-file-text" size="small" class="bg-green-100 text-green-600" />
                <span class="font-semibold">Text Content</span>
              </div>
            </template>
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-surface-700">Content</label>
              <Textarea
                v-model="item.content"
                placeholder="Enter your text content"
                rows="8"
                class="w-full"
              />
            </div>
          </Panel>

          <!-- Link Type Content -->
          <Panel v-else-if="item.type === 'link'" header="Link Details" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-link" size="small" class="bg-blue-100 text-blue-600" />
                <span class="font-semibold">Link Details</span>
              </div>
            </template>
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-surface-700">URL</label>
              <InputText
                v-model="item.url"
                placeholder="Enter URL"
                class="w-full"
              />
            </div>
          </Panel>

          <!-- File Upload Types -->
          <Panel v-else-if="['image', 'map', 'diagram'].includes(item?.type as string)" :header="getFileUploadHeader()" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar :icon="getFileUploadIcon()" size="small" :class="getFileUploadIconClass()" />
                <span class="font-semibold">{{ getFileUploadHeader() }}</span>
              </div>
            </template>
            <div class="space-y-4">
              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Upload {{ item.type }}</label>
                <FileUpload
                  ref="fileUpload"
                  mode="basic"
                  accept="image/*"
                  :maxFileSize="10000000"
                  :auto="false"
                  choose-label="Choose Image"
                  class="w-full"
                  @select="onFileSelect"
                  :disabled="uploadingFile"
                />
                <small class="text-surface-500">Maximum file size: 10MB. Supported formats: JPEG, PNG, GIF, WebP</small>
              </div>

              <!-- Upload Progress -->
              <div v-if="uploadingFile" class="space-y-2">
                <div class="flex items-center gap-2">
                  <i class="pi pi-spin pi-spinner text-primary-500"></i>
                  <span class="text-sm text-surface-600">Uploading image...</span>
                </div>
                <ProgressBar :value="uploadProgress" class="h-2" />
              </div>

              <!-- Current Image Preview -->
              <div v-if="item.imageUrl && !uploadingFile" class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Current Image</label>
                <div class="border border-surface-200 rounded-lg p-3">
                  <img
                    :src="item.imageUrl"
                    :alt="item.title"
                    class="max-w-full h-auto max-h-48 rounded-lg mx-auto block"
                    @error="handleImageError"
                  />
                  <div class="mt-2 text-xs text-surface-500 text-center">
                    {{ item.imageFileName }} ({{ formatFileSize(item.imageSize) }})
                  </div>
                </div>
              </div>

              <!-- Selected File Preview -->
              <div v-if="selectedFile && !uploadingFile" class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Selected File</label>
                <div class="border border-surface-200 rounded-lg p-3">
                  <div class="flex items-center gap-3">
                    <Avatar :icon="getFileUploadIcon()" size="small" :class="getFileUploadIconClass()" />
                    <div class="flex-1">
                      <p class="text-sm font-medium">{{ selectedFile.name }}</p>
                      <p class="text-xs text-surface-500">{{ formatFileSize(selectedFile.size) }}</p>
                    </div>
                    <Button
                      icon="pi pi-times"
                      size="small"
                      text
                      severity="danger"
                      @click="clearSelectedFile"
                      v-tooltip="'Remove file'"
                    />
                  </div>
                </div>
              </div>
            </div>
          </Panel>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="Cancel"
          icon="pi pi-times"
          outlined
          @click="handleClose"
        />
        <Button
          label="Save Item"
          icon="pi pi-check"
          :disabled="!canSave"
          :loading="store.actionLoading.addingItem"
          @click="saveItem"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useBookStore } from '../../stores/book';
import { useAuthStore } from '../../stores/auth';
import type { Item, TestQuestion } from '../../types/item';
import { buildApiUrl } from '../../config/api';
import Dialog from 'primevue/dialog';
import Select from 'primevue/select';
import InputText from 'primevue/inputtext';
import InputNumber from 'primevue/inputnumber';
import Textarea from 'primevue/textarea';
import FileUpload from 'primevue/fileupload';
import RadioButton from 'primevue/radiobutton';
import Button from 'primevue/button';
import Panel from 'primevue/panel';
import Avatar from 'primevue/avatar';
import Badge from 'primevue/badge';
import ProgressBar from 'primevue/progressbar';

interface Props {
  visible: boolean;
  chapterId?: string;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'itemCreated', item: Item): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const store = useBookStore();
const authStore = useAuthStore();
const route = useRoute();

const item = ref<Partial<Item>>({
  type: undefined,
  title: '',
  options: [],
  correctAnswer: undefined,
  timedAnswer: '',
  revealTimeSeconds: 30,
  testQuestions: []
});

// Test question navigation
const currentTestQuestionIndex = ref(0);

// File upload state
const selectedFile = ref<File | null>(null);
const uploadingFile = ref(false);
const uploadProgress = ref(0);
const fileUpload = ref();

const currentTestQuestion = computed(() => {
  if (!item.value.testQuestions || item.value.testQuestions.length === 0) {
    return null;
  }
  return item.value.testQuestions[currentTestQuestionIndex.value];
});

const itemTypes = [
  { label: 'Question', value: 'question' },
  { label: 'Test', value: 'test' },
  { label: 'Text Content', value: 'text' },
  { label: 'Image Upload', value: 'image' },
  { label: 'Link Details', value: 'link' },
  { label: 'Map Upload', value: 'map' },
  { label: 'Diagram Upload', value: 'diagram' },
  { label: 'Timed Question', value: 'timed-question' }
];

const dialogTitle = computed(() => {
  return 'Create New Item';
});

const dialogIcon = computed(() => {
  return 'pi pi-plus';
});

const dialogIconClass = computed(() => {
  return 'bg-purple-100 text-purple-600';
});

const canSave = computed(() => {
  return item.value.type && item.value.title;
});

// Watch for type changes to initialize appropriate data structures
watch(() => item.value.type, (newType) => {
  if (newType === 'question' && !item.value.options) {
    item.value.options = [];
  }
  if (newType === 'test' && !item.value.testQuestions) {
    item.value.testQuestions = [];
  }
});

// Question methods
const addOption = () => {
  item.value.options = [...(item.value.options || []), { text: '' }];
};

const removeOption = (index: number) => {
  if (item.value.options) {
    item.value.options.splice(index, 1);
    if (item.value.correctAnswer !== undefined && item.value.correctAnswer >= index) {
      if (item.value.correctAnswer === index) {
        item.value.correctAnswer = undefined;
      } else {
        item.value.correctAnswer--;
      }
    }
  }
};

// Test navigation methods
const nextTestQuestion = () => {
  if (item.value.testQuestions && currentTestQuestionIndex.value < item.value.testQuestions.length - 1) {
    currentTestQuestionIndex.value++;
  }
};

const previousTestQuestion = () => {
  if (currentTestQuestionIndex.value > 0) {
    currentTestQuestionIndex.value--;
  }
};

// Test methods
const addTestQuestion = () => {
  if (!item.value.testQuestions) {
    item.value.testQuestions = [];
  }
  const newQuestion: TestQuestion = {
    id: `q-${Date.now()}`,
    question: '',
    options: [{ text: '' }, { text: '' }],
    correctAnswer: 0
  };
  item.value.testQuestions.push(newQuestion);
  // Navigate to the new question
  currentTestQuestionIndex.value = item.value.testQuestions.length - 1;
};

const removeTestQuestion = (index: number) => {
  if (item.value.testQuestions) {
    item.value.testQuestions.splice(index, 1);
    // Adjust current index if needed
    if (currentTestQuestionIndex.value >= item.value.testQuestions.length) {
      currentTestQuestionIndex.value = Math.max(0, item.value.testQuestions.length - 1);
    }
  }
};

const addTestQuestionOption = (questionIndex: number) => {
  if (item.value.testQuestions && item.value.testQuestions[questionIndex]) {
    item.value.testQuestions[questionIndex].options.push({ text: '' });
  }
};

const removeTestQuestionOption = (questionIndex: number, optionIndex: number) => {
  if (item.value.testQuestions && item.value.testQuestions[questionIndex]) {
    const question = item.value.testQuestions[questionIndex];
    question.options.splice(optionIndex, 1);
    if (question.correctAnswer >= optionIndex) {
      if (question.correctAnswer === optionIndex) {
        question.correctAnswer = 0;
      } else {
        question.correctAnswer--;
      }
    }
  }
};

// File upload helpers
const getFileUploadHeader = () => {
  const headers = {
    image: 'Image Upload',
    map: 'Map Upload',
    diagram: 'Diagram Upload'
  };
  return headers[item.value.type as keyof typeof headers] || 'File Upload';
};

const getFileUploadIcon = () => {
  const icons = {
    image: 'pi pi-image',
    map: 'pi pi-map',
    diagram: 'pi pi-sitemap'
  };
  return icons[item.value.type as keyof typeof icons] || 'pi pi-file';
};

const getFileUploadIconClass = () => {
  const classes = {
    image: 'bg-pink-100 text-pink-600',
    map: 'bg-teal-100 text-teal-600',
    diagram: 'bg-indigo-100 text-indigo-600'
  };
  return classes[item.value.type as keyof typeof classes] || 'bg-surface-100 text-surface-600';
};

const saveItem = async () => {
  if (props.chapterId && item.value.type && item.value.title && route.params.id) {
    try {
      const { id, ...itemData } = { ...item.value } as Item;
      const newItem = await store.addItem(route.params.id as string, props.chapterId, itemData);

      // Upload image if one is selected and item type supports images
      if (selectedFile.value && ['image', 'map', 'diagram'].includes(item.value.type)) {
        await uploadImageToItem(newItem.id);
      }

      // Reset form
      item.value = {
        type: undefined,
        title: '',
        options: [],
        correctAnswer: undefined,
        timedAnswer: '',
        revealTimeSeconds: 30,
        testQuestions: []
      };

      // Reset file upload state
      selectedFile.value = null;
      uploadingFile.value = false;
      uploadProgress.value = 0;

      emit('itemCreated', newItem);
      emit('update:visible', false);
    } catch (error) {
      console.error('Error saving item:', error);
      // Handle error appropriately
    }
  }
};

// File upload methods
const onFileSelect = (event: any) => {
  const files = event.files;
  if (files && files.length > 0) {
    selectedFile.value = files[0];
  }
};

const clearSelectedFile = () => {
  selectedFile.value = null;
  if (fileUpload.value) {
    fileUpload.value.clear();
  }
};

const formatFileSize = (bytes: number | undefined): string => {
  if (!bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const handleImageError = (event: Event) => {
  console.error('Error loading image:', event);
  // Could show a placeholder or error message
};

const uploadImageToItem = async (itemId: string): Promise<boolean> => {
  if (!selectedFile.value || !props.chapterId || !route.params.id) {
    return false;
  }

  try {
    uploadingFile.value = true;
    uploadProgress.value = 0;

    const formData = new FormData();
    formData.append('image', selectedFile.value);

    const authHeaders = await authStore.getAuthHeaders();
    const response = await fetch(
      buildApiUrl(`/api/books/${route.params.id}/chapters/${props.chapterId}/items/${itemId}/upload-image`),
      {
        method: 'POST',
        headers: {
          ...authHeaders,
        },
        body: formData,
      }
    );

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('Image uploaded successfully:', result);
    return true;
  } catch (error) {
    console.error('Error uploading image:', error);
    return false;
  } finally {
    uploadingFile.value = false;
    uploadProgress.value = 0;
  }
};

const handleClose = () => {
  // Reset form when closing
  item.value = {
    type: undefined,
    title: '',
    options: [],
    correctAnswer: undefined,
    timedAnswer: '',
    revealTimeSeconds: 30,
    testQuestions: []
  };
  // Reset file upload state
  selectedFile.value = null;
  uploadingFile.value = false;
  uploadProgress.value = 0;
  // Reset test question navigation
  currentTestQuestionIndex.value = 0;
  emit('update:visible', false);
};

// Watch for item type changes to reset test question navigation
watch(() => item.value.type, () => {
  currentTestQuestionIndex.value = 0;
});

// Watch for dialog visibility to reset navigation
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    currentTestQuestionIndex.value = 0;
  }
});
</script>

<style scoped>
.item-form-dialog :deep(.p-dialog) {
  border-radius: 12px;
}

.item-form-dialog :deep(.p-dialog-header) {
  border-radius: 12px 12px 0 0;
}
</style>
