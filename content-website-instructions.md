# Vue.js Content Serving Website Instructions

## Project Overview
Build a standalone Vue.js website that serves educational content by fetching data from the textbook platform server API. This website will be accessed via QR codes and should provide an optimized, mobile-friendly experience for displaying various types of educational activities.

## Technical Requirements

### Framework & Dependencies
- **Vue 3** with Composition API
- **TypeScript** for type safety
- **Vite** for build tooling
- **Vue Router** for navigation
- **Axios** or **Fetch API** for HTTP requests
- **TailwindCSS** for styling
- **PrimeVue** components (optional, for consistency with main platform)

### Server API Integration
**Base Server URL:** 
- Production: `https://my-textbook-app-************.us-central1.run.app`
- Development: `http://localhost:8080` (or network IP)

**Key API Endpoints:**
- `GET /api/qr-info/:bookId/:chapterId/:itemId` - Get item metadata and content
- `GET /api/images/:bookId/:chapterId/:fileName` - Get uploaded images
- `GET /item/:bookId/:chapterId/:itemId` - Server-rendered page (for reference only)

**Primary Data Endpoint:**
The frontend should use `/api/qr-info/:bookId/:chapterId/:itemId` as the main data source.

**API Response Structure for `/api/qr-info`:**
```typescript
interface QRInfoResponse {
  data: {
    bookId: string;
    chapterId: string;
    itemId: string;
    bookTitle: string;
    chapterTitle: string;
    itemTitle: string;
    itemType: string;
    url: string; // The display URL
  };
  item: Item; // Complete item object with all fields
  displayUrl: string;
  cached: boolean; // QR cache status
  qrUrl: string; // QR code endpoint path
  cacheKey: string;
}
```

## Data Structures

### Item Interface
```typescript
interface Option {
  text: string;
}

interface TestQuestion {
  id: string;
  question: string;
  options: Option[];
  correctAnswer: number;
}

interface Item {
  id: string;
  title: string;
  type: 'question' | 'text' | 'image' | 'link' | 'map' | 'diagram' | 'timed-question' | 'test';
  
  // Question fields
  question?: string;
  options?: Option[];
  correctAnswer?: number;
  
  // Text content
  content?: string;
  
  // Link fields
  url?: string;
  
  // Image/Map/Diagram fields
  imageUrl?: string;
  imagePath?: string;
  imageFileName?: string;
  imageSize?: number;
  imageMimeType?: string;
  
  // Timed question fields
  timedAnswer?: string;
  revealTimeSeconds?: number;
  
  // Test fields
  testQuestions?: TestQuestion[];
}
```

### Book & Chapter Context
```typescript
interface BookContext {
  bookId: string;
  bookTitle: string;
  chapterId: string;
  chapterTitle: string;
}
```

## Component Architecture

### 1. Main App Component
- Handle routing based on URL parameters
- Fetch item data from server
- Display appropriate component based on item type
- Handle loading states and error handling

### 2. Item Type Components

#### QuestionComponent.vue
**Purpose:** Display multiple choice questions with interactive options
**Features:**
- Shuffle options on each load (randomization)
- Click to select answers
- Show correct/incorrect feedback
- Visual feedback with colors (green for correct, red for incorrect)
- Option letters (A, B, C, D)

#### TestComponent.vue
**Purpose:** Display multi-question tests with scoring
**Features:**
- Randomize question order
- Randomize options within each question
- Question navigation (previous/next)
- Progress tracking
- Score calculation and display
- Results summary with radial progress indicators
- Download/screenshot functionality for results
- User name input for results

#### TextComponent.vue
**Purpose:** Display rich text content
**Features:**
- Render HTML content safely
- Responsive text layout
- Support for formatted text

#### ImageComponent.vue
**Purpose:** Display images with proper scaling
**Features:**
- Responsive image display
- Loading states
- Error handling for missing images
- Zoom functionality (optional)

#### LinkComponent.vue
**Purpose:** Handle external links
**Features:**
- Automatic redirect to external URLs
- Link preview (optional)
- Security considerations for external links

#### MapComponent.vue
**Purpose:** Display uploaded map images
**Features:**
- Similar to ImageComponent but optimized for maps
- Potential for interactive features (zoom, pan)

#### DiagramComponent.vue
**Purpose:** Display uploaded diagram images
**Features:**
- Similar to ImageComponent but optimized for diagrams
- Clear, high-resolution display

#### TimedQuestionComponent.vue
**Purpose:** Display questions with countdown timers
**Features:**
- Countdown timer display
- Start/stop/reset functionality
- Reveal answer after timer expires
- Visual timer with progress indication

## Routing Structure

```
/item/:bookId/:chapterId/:itemId
```

**Route Handler Logic:**
1. Extract parameters from URL
2. Fetch item data from `/api/qr-info` endpoint
3. Handle link redirects immediately (if item.type === 'link')
4. Determine item type and render appropriate component
5. Handle loading states and error cases

**Critical Implementation Notes:**
- **Link Handling:** If `item.type === 'link'` and `item.url` exists, immediately redirect using `window.location.href = item.url`
- **Image URLs:** For image/map/diagram items, use `item.imageUrl` directly (server provides full URLs)
- **Error Handling:** Show user-friendly error pages for 404s or network failures
- **Loading States:** Display loading indicators while fetching data

## Key Features to Implement

### 1. Randomization System
- **Questions:** Shuffle options array on component mount
- **Tests:** Shuffle both question order and options within questions
- Use consistent randomization algorithm (Fisher-Yates shuffle)

### 2. Mobile-First Design
- Responsive layouts for all screen sizes
- Touch-friendly interactions
- Optimized for portrait orientation
- Fast loading on mobile networks

### 3. Offline Considerations
- Cache critical assets
- Graceful degradation when server unavailable
- Service worker for basic offline functionality

### 4. Performance Optimization
- Lazy loading for images
- Component code splitting
- Minimal bundle size
- Fast initial page load

### 5. Error Handling
- Network error handling
- Missing item/book/chapter handling
- Image loading failures
- Graceful fallbacks

## Styling Guidelines

### Theme System
Support dynamic theming based on book configuration:
```typescript
interface Theme {
  primaryColor: string;    // Default: '#4f46e5'
  secondaryColor: string;  // Default: '#7c3aed'
  accentColor: string;     // Default: '#F59E0B'
}
```

### Visual Design
- Clean, modern interface
- High contrast for readability
- Consistent spacing and typography
- Smooth animations and transitions
- Icons for different item types:
  - Question: ❓
  - Test: 📝
  - Text: 📄
  - Image: 🖼️
  - Link: 🔗
  - Map: 🗺️
  - Diagram: 📊
  - Timed Question: ⏰

## Development Workflow

### 1. Project Setup
```bash
npm create vue@latest content-website
cd content-website
npm install
npm install axios tailwindcss @tailwindcss/typography
```

### 2. Environment Configuration
Create environment variables for server URLs:
```env
VITE_API_BASE_URL=https://my-textbook-app-************.us-central1.run.app
VITE_DEV_API_BASE_URL=http://localhost:8080
```

### 3. Testing Strategy
- Unit tests for individual components
- Integration tests for API communication
- E2E tests for complete user flows
- Mobile device testing

### 4. Deployment
- Build optimized production bundle
- Deploy to CDN or static hosting
- Configure proper caching headers
- Set up monitoring and analytics

## Security Considerations

### 1. Content Security
- Sanitize HTML content in text items
- Validate image URLs
- Secure handling of external links

### 2. API Security
- Handle CORS properly
- Validate server responses
- Implement rate limiting awareness

## Performance Targets
- First Contentful Paint: < 1.5s
- Largest Contentful Paint: < 2.5s
- Cumulative Layout Shift: < 0.1
- First Input Delay: < 100ms

## Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Progressive enhancement for older browsers

## Implementation Examples

### Sample API Service
```typescript
// services/api.ts
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

export class ContentAPI {
  static async getItemInfo(bookId: string, chapterId: string, itemId: string): Promise<QRInfoResponse> {
    const response = await axios.get(`${API_BASE_URL}/api/qr-info/${bookId}/${chapterId}/${itemId}`);
    return response.data;
  }

  static getImageUrl(bookId: string, chapterId: string, fileName: string): string {
    return `${API_BASE_URL}/api/images/${bookId}/${chapterId}/${fileName}`;
  }

  // Handle link redirects (for link-type items)
  static shouldRedirect(item: Item): boolean {
    return item.type === 'link' && !!item.url;
  }

  static redirect(url: string): void {
    window.location.href = url;
  }
}

// Error handling wrapper
export async function safeApiCall<T>(apiCall: () => Promise<T>): Promise<T | null> {
  try {
    return await apiCall();
  } catch (error) {
    console.error('API call failed:', error);
    return null;
  }
}
```

### Sample Question Component Structure
```vue
<template>
  <div class="question-container">
    <h2 class="question-title">{{ question.question || question.title }}</h2>
    <div class="options-container">
      <div
        v-for="(option, index) in shuffledOptions"
        :key="index"
        @click="selectOption(index)"
        class="option"
        :class="getOptionClass(index)"
      >
        <span class="option-letter">{{ String.fromCharCode(65 + index) }}</span>
        <span class="option-text">{{ option.text }}</span>
      </div>
    </div>
    <div v-if="showResult" class="result-container">
      <div class="result-message" :class="resultClass">
        {{ resultMessage }}
      </div>
    </div>
  </div>
</template>
```

### Sample Test Component Features
```typescript
// Test component logic
interface TestState {
  currentQuestionIndex: number;
  answers: number[];
  showResults: boolean;
  score: number;
  shuffledQuestions: TestQuestion[];
}

const calculateScore = (answers: number[], questions: TestQuestion[]) => {
  const correct = answers.filter((answer, index) =>
    answer === questions[index].correctAnswer
  ).length;
  return Math.round((correct / questions.length) * 100);
};
```

### Sample Routing Setup
```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router';
import ContentDisplay from '@/views/ContentDisplay.vue';

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/item/:bookId/:chapterId/:itemId',
      name: 'content',
      component: ContentDisplay,
      props: true
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: '/'
    }
  ]
});
```

## Additional Considerations

### 1. Analytics Integration
- Track item views and interactions
- Monitor performance metrics
- User engagement analytics

### 2. Accessibility
- ARIA labels for interactive elements
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support

### 3. Internationalization
- Support for RTL languages (Arabic support mentioned in memories)
- Text direction handling
- Font loading for different languages

### 4. Progressive Web App Features
- Service worker for caching
- Offline functionality
- Add to home screen capability
- Push notifications (optional)

## Missing Implementation Details

### 1. PDF Generation for Test Results
The current server includes PDF generation functionality for test results. The frontend should:
- Use the server's existing PDF generation by making a POST request to a PDF endpoint (to be created)
- Include user name, test scores, and formatted results
- Support Arabic language text (bidi-js is already available on server)

### 2. Randomization Algorithm
Implement Fisher-Yates shuffle consistently:
```typescript
function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}
```

### 3. Environment Detection
```typescript
// utils/environment.ts
export const getApiBaseUrl = (): string => {
  if (import.meta.env.PROD) {
    return 'https://my-textbook-app-************.us-central1.run.app';
  }
  return import.meta.env.VITE_DEV_API_BASE_URL || 'http://localhost:8080';
};
```

### 4. Image Handling
- All image URLs are provided complete by the server (no need to construct them)
- Handle both Firebase Storage URLs and external URLs
- Implement proper error handling for missing images
- Use lazy loading for performance

### 5. Test Results Features
Based on server implementation, test results should include:
- Radial progress indicators for scores
- User name input and storage
- Download functionality (PDF generation via server)
- Question-by-question breakdown
- Retry functionality

This website will provide a fast, efficient way to serve educational content without requiring server-side rendering, making it easier to iterate and deploy independently from the main platform.
