<template>
  <div class="p-4 h-full max-h-9/10">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 h-full min-h-0">
        <!-- Basic Information Section (Left Column) -->
        <div class="lg:col-span-1">
          <Panel header="Basic Information" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-info-circle" size="small" class="bg-blue-100 text-blue-600" />
                <span class="font-semibold">Basic Information</span>
              </div>
            </template>
            <div class="space-y-3">
              <div class="space-y-2">
                <label for="itemType" class="block text-sm font-semibold text-surface-700">Item Type</label>
                <Select
                  id="itemType"
                  v-model="item.type"
                  :options="itemTypes"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select Item Type"
                  class="w-full"
                />
              </div>
              <div class="space-y-2">
                <label for="itemTitle" class="block text-sm font-semibold text-surface-700">Title</label>
                <InputText
                  id="itemTitle"
                  v-model="item.title"
                  placeholder="Enter item title"
                  class="w-full"
                />
              </div>
            </div>
          </Panel>
        </div>

        <!-- Content Section (Right Column) -->
        <div class="lg:col-span-1 space-y-3">
          <!-- Question Type Content -->
          <Panel v-if="item.type === 'question'" header="Question Details" class="!rounded-xl !h-full">
          <template #header>
            <div class="flex items-center gap-2">
              <Avatar icon="pi pi-question-circle" size="small" class="bg-orange-100 text-orange-600" />
              <span class="font-semibold">Question Details</span>
            </div>
          </template>
            <div class="space-y-4">
              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Question Text</label>
                <InputText
                  v-model="item.question"
                  placeholder="Enter your question"
                  class="w-full"
                />
              </div>

              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <label class="block text-sm font-semibold text-surface-700">Answer Options</label>
                  <Button
                    label="Add Option"
                    icon="pi pi-plus"
                    size="small"
                    outlined
                    @click="addOption"
                    class="transition-all duration-200"
                  />
                </div>
                <div class="space-y-4 max-h-74 overflow-y-auto  no-scrollbar">
                  <div v-for="(option, index) in item.options" :key="index"
                       class="flex items-center gap-3 rounded-lg hover:border-primary-300 transition-colors">
                    <RadioButton
                      :value="index"
                      v-model="item.correctAnswer"
                      :inputId="`option-${index}`"
                    />
                    <InputText
                      v-model="option.text"
                      placeholder="Enter option text"
                      fluid
                    />
                    <Badge
                      v-if="index === item.correctAnswer"
                      value="Correct"
                      severity="success"
                      class="ml-2"
                    />
                    <Button
                      v-if="item.options && item.options.length > 2"
                      icon="pi pi-trash"
                      size="small"
                      severity="danger"
                      outlined
                      @click="removeOption(index)"
                      class="transition-all duration-200"
                    />
                  </div>
                </div>
              </div>
            </div>
          </Panel>

          <!-- Test Type Content -->
          <Panel v-else-if="item.type === 'test'" header="Test Details" class="!rounded-xl !h-full">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-list-check" size="small" class="bg-purple-100 text-purple-600" />
                <span class="font-semibold">Test Details</span>
              </div>
            </template>
            <div class="space-y-4">
              <!-- Test Questions Header with Navigation -->
              <div class="flex items-center justify-between">
                <label class="block text-sm font-semibold text-surface-700">Test Questions</label>
                <div class="flex items-center gap-2">
                  <Button
                    label="Add Question"
                    icon="pi pi-plus"
                    size="small"
                    outlined
                    @click="addTestQuestion"
                    class="transition-all duration-200"
                  />
                  <div v-if="item.testQuestions && item.testQuestions.length > 0" class="flex items-center gap-2 ml-4">
                    <Button
                      icon="pi pi-chevron-left"
                      size="small"
                      outlined
                      @click="previousTestQuestion"
                      :disabled="currentTestQuestionIndex === 0"
                      v-tooltip="'Previous Question'"
                    />
                    <span class="text-sm font-medium px-2">
                      {{ currentTestQuestionIndex + 1 }}/{{ item.testQuestions.length }}
                    </span>
                    <Button
                      icon="pi pi-chevron-right"
                      size="small"
                      outlined
                      @click="nextTestQuestion"
                      :disabled="currentTestQuestionIndex === item.testQuestions.length - 1"
                      v-tooltip="'Next Question'"
                    />
                  </div>
                </div>
              </div>

              <!-- Current Test Question Editor -->
              <div v-if="currentTestQuestion" class="border border-surface-200 rounded-lg p-4 space-y-3">
                <div class="flex items-center justify-between">
                  <h4 class="font-medium text-surface-700">Question {{ currentTestQuestionIndex + 1 }}</h4>
                  <Button
                    icon="pi pi-trash"
                    size="small"
                    severity="danger"
                    text
                    @click="removeTestQuestion(currentTestQuestionIndex)"
                    v-tooltip="'Delete Question'"
                  />
                </div>

                <div class="space-y-2">
                  <label class="text-sm font-medium text-surface-600">Question Text</label>
                  <InputText
                    v-model="currentTestQuestion.question"
                    placeholder="Enter question text"
                    class="w-full"
                  />
                </div>

                <div class="space-y-2">
                  <div class="flex items-center justify-between">
                    <label class="text-sm font-medium text-surface-600">Answer Options</label>
                    <Button
                      label="Add Option"
                      icon="pi pi-plus"
                      size="small"
                      text
                      @click="addTestQuestionOption(currentTestQuestionIndex)"
                    />
                  </div>

                  <div v-for="(option, oIndex) in currentTestQuestion.options" :key="oIndex"
                       class="flex items-center gap-2">
                    <RadioButton
                      :value="oIndex"
                      v-model="currentTestQuestion.correctAnswer"
                      :inputId="`test-q${currentTestQuestionIndex}-option-${oIndex}`"
                    />
                    <InputText
                      v-model="option.text"
                      placeholder="Enter option text"
                      class="flex-1"
                    />
                    <Badge
                      v-if="oIndex === currentTestQuestion.correctAnswer"
                      value="Correct"
                      severity="success"
                      class="ml-2"
                    />
                    <Button
                      icon="pi pi-trash"
                      size="small"
                      severity="danger"
                      text
                      @click="removeTestQuestionOption(currentTestQuestionIndex, oIndex)"
                      :disabled="currentTestQuestion.options.length <= 2"
                      v-tooltip="'Delete Option'"
                    />
                  </div>
                </div>
              </div>

              <!-- No Questions State -->
              <div v-else-if="!item.testQuestions || item.testQuestions.length === 0"
                   class="text-center py-8 border-2 border-dashed border-surface-300 rounded-lg">
                <Avatar icon="pi pi-question-circle" size="large" class="mb-3 bg-surface-100 text-surface-400" />
                <p class="text-surface-500 mb-3">No test questions yet</p>
                <Button
                  label="Add First Question"
                  icon="pi pi-plus"
                  @click="addTestQuestion"
                  outlined
                />
              </div>
            </div>
          </Panel>

          <!-- Text Type Content -->
          <Panel v-else-if="item.type === 'text'" header="Text Content" class="!rounded-xl !h-full">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-file-edit" size="small" class="bg-blue-100 text-blue-600" />
                <span class="font-semibold">Text Content</span>
              </div>
            </template>
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-surface-700">Content</label>
              <Textarea
                v-model="item.content"
                placeholder="Enter your text content"
                rows="4"
                class="w-full resize-none"
              />
            </div>
          </Panel>

          <!-- Image, Map, or Diagram Upload -->
          <Panel v-if="item.type && ['image', 'map', 'diagram'].includes(item.type)"
                 :header="`${item.type.charAt(0).toUpperCase() + item.type.slice(1)} Content`"
                 class="!rounded-xl !h-full">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar :icon="getTypeIcon(item.type)" size="small" :class="getTypeColor(item.type)" />
                <span class="font-semibold">{{ item.type.charAt(0).toUpperCase() + item.type.slice(1) }} Content</span>
              </div>
            </template>
            <div class="space-y-3">
              <div class="flex flex-col gap-2">
                <div class="flex items-center gap-4">
                  <RadioButton v-model="imageSourceType" inputId="upload" value="upload" />
                  <label for="upload">Upload File</label>
                  <RadioButton v-model="imageSourceType" inputId="url" value="url" />
                  <label for="url">Use URL</label>
                </div>
              </div>

              <div v-if="imageSourceType === 'upload'">
                <div v-if="!uploading && !item.imageUrl"
                     class="border-2 border-dashed border-surface-300 rounded-lg p-6 text-center hover:border-primary-300 transition-colors cursor-pointer"
                     @click="triggerFileInput">
                  <Avatar :icon="getTypeIcon(item.type)" size="large" class="mb-3 bg-surface-100 text-surface-400" />
                  <p class="text-sm text-surface-500 mt-2">Click to upload {{ item.type }}</p>
                  <input ref="fileInput" type="file" accept="image/*" class="hidden" @change="handleFileUpload" />
                </div>

                <div v-if="uploading" class="text-center">
                  <ProgressSpinner />
                  <p class="text-sm text-surface-500 mt-2">Uploading...</p>
                </div>

                <div v-if="item.imageUrl" class="relative">
                  <img :src="item.imageUrl" :alt="item.title" class="w-full h-auto rounded-lg" />
                  <Button icon="pi pi-times" class="absolute top-2 right-2" @click="removeImage" />
                </div>
              </div>

              <div v-else-if="imageSourceType === 'url'">
                <label class="block text-sm font-semibold text-surface-700">Image URL</label>
                <InputText
                  v-model="item.url"
                  placeholder="Enter image URL"
                  class="w-full"
                />
              </div>
            </div>
          </Panel>

          <!-- Link Type Content -->
          <Panel v-else-if="item.type === 'link'" header="Link Details" class="border-0 shadow-none">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-link" size="small" class="bg-green-100 text-green-600" />
                <span class="font-semibold">Link Details</span>
              </div>
            </template>
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-surface-700">URL</label>
              <InputText
                v-model="item.url"
                placeholder="https://example.com"
                class="w-full"
              />
            </div>
          </Panel>

          <!-- Timed Question Type Content -->
          <Panel v-else-if="item.type === 'timed-question'" header="Timed Question" class="border-0 shadow-none">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-clock" size="small" class="bg-amber-100 text-amber-600" />
                <span class="font-semibold">Timed Question</span>
              </div>
            </template>
            <div class="space-y-4">
              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Question</label>
                <InputText
                  v-model="item.question"
                  placeholder="Enter your open-ended question"
                  class="w-full"
                />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Predefined Answer</label>
                <Textarea
                  v-model="item.timedAnswer"
                  placeholder="Enter the answer that will be revealed after the timer"
                  rows="3"
                  class="w-full resize-none"
                />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Reveal Time (seconds)</label>
                <InputNumber
                  v-model="item.revealTimeSeconds"
                  type="number"
                  placeholder="30"
                  :min="5"
                  :max="300"
                  class="w-full"
                />
                <small class="text-surface-500">Time before the answer is revealed (5-300 seconds)</small>
              </div>
            </div>
          </Panel>
        </div>
      </div>
      <Divider />
      <!-- Save Button Section -->
        <div class="flex justify-end">
          <Button
            label="Save Item"
            icon="pi pi-save"
            @click="saveItem"
            :disabled="!item.type || !item.title"
            class="px-6 py-2 transition-all duration-200"
          />
        </div>

      <!-- Success Message -->
      <div v-if="lastCreatedItem" class="mt-4 border border-green-200 bg-green-50 rounded-xl p-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <Avatar
              icon="pi pi-check-circle"
              size="small"
              class="bg-green-100 text-green-600"
            />
            <div>
              <h3 class="font-semibold text-green-800">Item Created Successfully!</h3>
              <p class="text-sm text-green-700">"{{ lastCreatedItem.title }}" - Select from the tree to view QR code</p>
            </div>
          </div>
          <Button
            icon="pi pi-times"
            size="small"
            text
            @click="lastCreatedItem = null"
            class="text-green-600 hover:bg-green-100"
          />
        </div>
      </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useBookStore } from '../../stores/book';
import type { Item } from '../../types/item';
import Select  from 'primevue/select';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import RadioButton from 'primevue/radiobutton';
import Button from 'primevue/button';
import Panel from 'primevue/panel';
import Avatar from 'primevue/avatar';
import Badge from 'primevue/badge';
import Divider from 'primevue/divider';
import InputNumber from 'primevue/inputnumber';
import ProgressSpinner from 'primevue/progressspinner';
import { FirebaseStorageService } from '../../services/firebase-storage';

interface Props {
  chapterId?: string;
  onItemCreated?: () => void;
}

const props = defineProps<Props>();
const store = useBookStore();
const route = useRoute();
import Tooltip from 'primevue/tooltip';

const item = ref<Item>({
  id: '', // Will be generated on save
  title: '',
  type: 'text', // Initial state, will be set by user
  question: '',
  options: [],
  correctAnswer: undefined,
  content: '',
  url: '',
  imageUrl: '',
  imagePath: '',
  imageFileName: '',
  imageSize: 0,
  imageMimeType: '',
  timedAnswer: '',
  revealTimeSeconds: 30,
  testQuestions: [],
});

const imageSourceType = ref<'upload' | 'url'>('upload'); // Default to upload



// Register the v-tooltip directive
const vTooltip = Tooltip;
const lastCreatedItem = ref<Item | null>(null);
const uploading = ref(false);
const fileInput = ref<HTMLInputElement | null>(null);

// Test question navigation
const currentTestQuestionIndex = ref(0);

const currentTestQuestion = computed(() => {
  if (!item.value.testQuestions || item.value.testQuestions.length === 0) {
    return null;
  }
  return item.value.testQuestions[currentTestQuestionIndex.value];
});
const itemTypes = [
  { label: 'Question', value: 'question' },
  { label: 'Test', value: 'test' },
  { label: 'Text Content', value: 'text' },
  { label: 'Image Upload', value: 'image' },
  { label: 'Link Details', value: 'link' },
  { label: 'Map Upload', value: 'map' },
  { label: 'Diagram Upload', value: 'diagram' },
  { label: 'Timed Question', value: 'timed-question' }
];

watch(() => props.chapterId, () => {
  item.value = {
    id: '',
    type: 'text',
    title: '',
    options: [],
    correctAnswer: undefined,
    timedAnswer: '',
    revealTimeSeconds: 30,
    testQuestions: [],
    imageUrl: '',
    imageFileName: '',
    imagePath: '',
    url: '', // Clear URL as well
  };
  lastCreatedItem.value = null; // Clear QR code when switching chapters
});

watch(imageSourceType, (newType) => {
  if (newType === 'upload') {
    item.value.url = ''; // Clear URL if switching to upload
  } else if (newType === 'url') {
    // Clear uploaded image data if switching to URL
    item.value.imageUrl = '';
    item.value.imagePath = '';
    item.value.imageFileName = '';
    item.value.imageSize = 0;
    item.value.imageMimeType = '';
    if (fileInput.value) {
      fileInput.value.value = ''; // Clear file input
    }
  }
});

const addOption = () => {
  item.value.options = [...(item.value.options || []), { text: '' }];
};

const removeOption = (index: number) => {
  if (item.value.options) {
    item.value.options.splice(index, 1);
    // Adjust correct answer if needed
    if (item.value.correctAnswer !== undefined && item.value.correctAnswer >= index) {
      if (item.value.correctAnswer === index) {
        item.value.correctAnswer = undefined;
      } else {
        item.value.correctAnswer--;
      }
    }
  }
};

// Test navigation methods
const nextTestQuestion = () => {
  if (item.value.testQuestions && currentTestQuestionIndex.value < item.value.testQuestions.length - 1) {
    currentTestQuestionIndex.value++;
  }
};

const previousTestQuestion = () => {
  if (currentTestQuestionIndex.value > 0) {
    currentTestQuestionIndex.value--;
  }
};

// Test methods
const addTestQuestion = () => {
  if (!item.value.testQuestions) {
    item.value.testQuestions = [];
  }
  const newQuestion = {
    id: `q-${Date.now()}`,
    question: '',
    options: [{ text: '' }, { text: '' }],
    correctAnswer: 0
  };
  item.value.testQuestions.push(newQuestion);
  // Navigate to the new question
  currentTestQuestionIndex.value = item.value.testQuestions.length - 1;
};

const removeTestQuestion = (index: number) => {
  if (item.value.testQuestions) {
    item.value.testQuestions.splice(index, 1);
    // Adjust current index if needed
    if (currentTestQuestionIndex.value >= item.value.testQuestions.length) {
      currentTestQuestionIndex.value = Math.max(0, item.value.testQuestions.length - 1);
    }
  }
};

const addTestQuestionOption = (questionIndex: number) => {
  if (item.value.testQuestions && item.value.testQuestions[questionIndex]) {
    item.value.testQuestions[questionIndex].options.push({ text: '' });
  }
};

const removeTestQuestionOption = (questionIndex: number, optionIndex: number) => {
  if (item.value.testQuestions && item.value.testQuestions[questionIndex]) {
    const question = item.value.testQuestions[questionIndex];
    question.options.splice(optionIndex, 1);
    if (question.correctAnswer >= optionIndex) {
      if (question.correctAnswer === optionIndex) {
        question.correctAnswer = 0;
      } else {
        question.correctAnswer--;
      }
    }
  }
};

const triggerFileInput = () => {
  fileInput.value?.click();
};

const handleFileUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files[0]) {
    const file = target.files[0];
    const validation = FirebaseStorageService.validateImageFile(file);
    if (!validation.valid) {
      alert(validation.error);
      return;
    }

    uploading.value = true;
    try {
      const bookId = route.params.id as string;
      const chapterId = props.chapterId as string;
      const itemId = `item_${Date.now()}`;
      const path = FirebaseStorageService.generateImagePath(bookId, chapterId, itemId, file.name);
      const result = await FirebaseStorageService.uploadFile(file, path);

      item.value.imageUrl = result.downloadURL;
      item.value.imagePath = result.path;
      item.value.imageFileName = result.fileName;
    } catch (error) {
      console.error('Error uploading image:', error);
    } finally {
      uploading.value = false;
    }
  }
};

const removeImage = async () => {
  if (item.value.imagePath) {
    try {
      await FirebaseStorageService.deleteFile(item.value.imagePath);
      item.value.imageUrl = '';
      item.value.imagePath = '';
      item.value.imageFileName = '';
    } catch (error) {
      console.error('Error deleting image:', error);
    }
  }
};

const saveItem = async () => {
  if (props.chapterId && item.value.type && item.value.title && route.params.id) {
    const { id, ...itemData } = { ...item.value } as Item;

    let newItem;
    if (item.value.type === 'image' && imageSourceType.value === 'upload') {
      // If it's an image and source is upload, send the file
      const file = fileInput.value?.files?.[0];
      if (file) {
        newItem = await store.addItem(route.params.id as string, props.chapterId, itemData, file);
      } else {
        // If no file selected but upload is chosen, clear image data
        itemData.imageUrl = '';
        itemData.imagePath = '';
        itemData.imageFileName = '';
        itemData.imageSize = 0;
        itemData.imageMimeType = '';
        newItem = await store.addItem(route.params.id as string, props.chapterId, itemData);
      }
    } else if (item.value.type === 'image' && imageSourceType.value === 'url') {
      // If it's an image and source is URL, send the URL
      itemData.imageUrl = itemData.url; // Ensure imageUrl is set from url
      itemData.imagePath = ''; // Clear imagePath
      itemData.imageFileName = '';
      itemData.imageSize = 0;
      itemData.imageMimeType = '';
      newItem = await store.addItem(route.params.id as string, props.chapterId, itemData);
    } else {
      // For other item types or non-image items, send itemData directly
      newItem = await store.addItem(route.params.id as string, props.chapterId, itemData);
    }

    if (newItem) {
      lastCreatedItem.value = newItem;
      item.value = {
        id: '',
        type: 'text',
        title: '',
        options: [],
        correctAnswer: undefined,
        timedAnswer: '',
        revealTimeSeconds: 30,
        testQuestions: [],
        imageUrl: '',
        imagePath: '',
        imageFileName: '',
        url: '',
      };

      // Call the callback to refresh the tree data
      if (props.onItemCreated) {
        props.onItemCreated();
      }

      // Reset test question navigation
      currentTestQuestionIndex.value = 0;
    }
  }
};

const getTypeIcon = (type: string | undefined) => {
  switch (type) {
    case 'image': return 'pi pi-image';
    case 'map': return 'pi pi-map';
    case 'diagram': return 'pi pi-chart-bar';
    default: return 'pi pi-file';
  }
};

const getTypeColor = (type: string | undefined) => {
  switch (type) {
    case 'image': return 'bg-purple-100 text-purple-600';
    case 'map': return 'bg-teal-100 text-teal-600';
    case 'diagram': return 'bg-indigo-100 text-indigo-600';
    default: return 'bg-surface-100 text-surface-600';
  }
};

// Watch for item type changes to reset test question navigation
watch(() => item.value.type, () => {
  currentTestQuestionIndex.value = 0;
});
</script>