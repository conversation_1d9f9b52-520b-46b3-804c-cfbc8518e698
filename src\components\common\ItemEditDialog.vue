<template>
  <Dialog
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    :header="dialogTitle"
    :style="{ width: '80vw', maxWidth: '1000px' }"
    :modal="true"
    :closable="true"
    :draggable="false"
    class="item-edit-dialog"
    @hide="handleClose"
  >
    <template #header>
      <div class="flex items-center gap-2">
        <Avatar 
          icon="pi pi-pencil" 
          size="small" 
          class="bg-orange-100 text-orange-600" 
        />
        <span class="font-semibold">{{ dialogTitle }}</span>
      </div>
    </template>

    <div class="p-4 max-h-[70vh] overflow-y-auto">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Basic Information Section (Left Column - Narrower) -->
        <div class="lg:col-span-1">
          <Panel header="Basic Information" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-info-circle" size="small" class="bg-blue-100 text-blue-600" />
                <span class="font-semibold">Basic Information</span>
              </div>
            </template>
            <div class="space-y-3">
              <div class="space-y-2">
                <label for="itemType" class="block text-sm font-semibold text-surface-700">Item Type</label>
                <Select
                  id="itemType"
                  v-model="editItem.type"
                  :options="itemTypeOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select Item Type"
                  class="w-full"
                  disabled
                />
              </div>
              
              <div class="space-y-2">
                <label for="itemTitle" class="block text-sm font-semibold text-surface-700">Title</label>
                <InputText
                  id="itemTitle"
                  v-model="editItem.title"
                  placeholder="Enter item title"
                  class="w-full"
                />
              </div>
            </div>
          </Panel>
        </div>

        <!-- Content Section (Right Column - Wider) -->
        <div class="lg:col-span-2 space-y-3">
          <!-- Question Type Content -->
          <Panel v-if="editItem.type === 'question'" header="Question Details" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-question-circle" size="small" class="bg-orange-100 text-orange-600" />
                <span class="font-semibold">Question Details</span>
              </div>
            </template>
            <div class="space-y-4">
              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Question Text</label>
                <InputText
                  v-model="editItem.question"
                  placeholder="Enter your question"
                  class="w-full"
                />
              </div>

              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <label class="block text-sm font-semibold text-surface-700">Answer Options</label>
                  <Button
                    label="Add Option"
                    icon="pi pi-plus"
                    size="small"
                    outlined
                    @click="addOption"
                    class="transition-all duration-200"
                  />
                </div>
                <div class="space-y-3">
                  <div v-for="(option, index) in editItem.options" :key="index"
                       class="flex items-center gap-3 p-4 border border-surface-200 rounded-lg hover:border-primary-300 transition-colors">
                    <RadioButton
                      :value="index"
                      v-model="editItem.correctAnswer"
                      :inputId="`edit-option-${index}`"
                    />
                    <InputText
                      v-model="option.text"
                      placeholder="Enter option text"
                      class="flex-1"
                    />
                    <Badge
                      v-if="index === editItem.correctAnswer"
                      value="Correct"
                      severity="success"
                      class="ml-2"
                    />
                    <Button
                      icon="pi pi-trash"
                      size="small"
                      severity="danger"
                      text
                      @click="removeOption(index)"
                      v-tooltip="'Remove Option'"
                      class="transition-all duration-200"
                    />
                  </div>
                </div>
              </div>
            </div>
          </Panel>

          <!-- Test Type Content -->
          <Panel v-if="editItem.type === 'test'" header="Test Configuration" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-file-edit" size="small" class="bg-purple-100 text-purple-600" />
                <span class="font-semibold">Test Configuration</span>
              </div>
            </template>
            <div class="space-y-4">
              <!-- Test Questions Header with Navigation -->
              <div class="flex items-center justify-between">
                <label class="block text-sm font-semibold text-surface-700">Test Questions</label>
                <div class="flex items-center gap-2">
                  <Button
                    label="Add Question"
                    icon="pi pi-plus"
                    size="small"
                    outlined
                    @click="addTestQuestion"
                  />
                  <div v-if="editItem.testQuestions && editItem.testQuestions.length > 0" class="flex items-center gap-2 ml-4">
                    <Button
                      icon="pi pi-chevron-left"
                      size="small"
                      outlined
                      @click="previousTestQuestion"
                      :disabled="currentTestQuestionIndex === 0"
                      v-tooltip="'Previous Question'"
                    />
                    <span class="text-sm font-medium px-2">
                      {{ currentTestQuestionIndex + 1 }}/{{ editItem.testQuestions.length }}
                    </span>
                    <Button
                      icon="pi pi-chevron-right"
                      size="small"
                      outlined
                      @click="nextTestQuestion"
                      :disabled="currentTestQuestionIndex === editItem.testQuestions.length - 1"
                      v-tooltip="'Next Question'"
                    />
                  </div>
                </div>
              </div>

              <!-- Current Test Question Editor -->
              <div v-if="currentTestQuestion" class="border border-surface-200 rounded-lg p-4 space-y-3">
                <div class="flex items-center justify-between">
                  <h4 class="font-medium text-surface-700">Question {{ currentTestQuestionIndex + 1 }}</h4>
                  <Button
                    icon="pi pi-trash"
                    size="small"
                    severity="danger"
                    text
                    @click="removeTestQuestion(currentTestQuestionIndex)"
                    v-tooltip="'Delete Question'"
                  />
                </div>

                <div class="space-y-2">
                  <label class="text-sm font-medium text-surface-600">Question Text</label>
                  <InputText
                    v-model="currentTestQuestion.question"
                    placeholder="Enter question text"
                    class="w-full"
                  />
                </div>

                <div class="space-y-2">
                  <div class="flex items-center justify-between">
                    <label class="text-sm font-medium text-surface-600">Answer Options</label>
                    <Button
                      label="Add Option"
                      icon="pi pi-plus"
                      size="small"
                      text
                      @click="addTestQuestionOption(currentTestQuestionIndex)"
                    />
                  </div>

                  <div v-for="(option, oIndex) in currentTestQuestion.options" :key="oIndex"
                       class="flex items-center gap-2">
                    <RadioButton
                      :value="oIndex"
                      v-model="currentTestQuestion.correctAnswer"
                      :inputId="`test-q${currentTestQuestionIndex}-option-${oIndex}`"
                    />
                    <InputText
                      v-model="option.text"
                      placeholder="Enter option text"
                      class="flex-1"
                    />
                    <Badge
                      v-if="oIndex === currentTestQuestion.correctAnswer"
                      value="Correct"
                      severity="success"
                      class="ml-2"
                    />
                    <Button
                      icon="pi pi-trash"
                      size="small"
                      severity="danger"
                      text
                      @click="removeTestQuestionOption(currentTestQuestionIndex, oIndex)"
                      :disabled="currentTestQuestion.options.length <= 2"
                      v-tooltip="'Delete Option'"
                    />
                  </div>
                </div>
              </div>

              <!-- No Questions State -->
              <div v-else-if="!editItem.testQuestions || editItem.testQuestions.length === 0"
                   class="text-center py-8 border-2 border-dashed border-surface-300 rounded-lg">
                <Avatar icon="pi pi-question-circle" size="large" class="mb-3 bg-surface-100 text-surface-400" />
                <p class="text-surface-500 mb-3">No test questions yet</p>
                <Button
                  label="Add First Question"
                  icon="pi pi-plus"
                  @click="addTestQuestion"
                  outlined
                />
              </div>
            </div>
          </Panel>

          <!-- Timed Question Type Content -->
          <Panel v-if="editItem.type === 'timed-question'" header="Timed Question Details" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-clock" size="small" class="bg-yellow-100 text-yellow-600" />
                <span class="font-semibold">Timed Question Details</span>
              </div>
            </template>
            <div class="space-y-4">
              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Question Text</label>
                <InputText
                  v-model="editItem.question"
                  placeholder="Enter your question"
                  class="w-full"
                />
              </div>
              
              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Timed Answer</label>
                <InputText
                  v-model="editItem.timedAnswer"
                  placeholder="Enter the answer that will be revealed"
                  class="w-full"
                />
              </div>
              
              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Reveal Time (seconds)</label>
                <InputNumber
                  v-model="editItem.revealTimeSeconds"
                  placeholder="30"
                  class="w-full"
                  :min="5"
                  :max="300"
                />
              </div>
            </div>
          </Panel>

          <!-- Text Type Content -->
          <Panel v-if="editItem.type === 'text'" header="Text Content" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-file-text" size="small" class="bg-green-100 text-green-600" />
                <span class="font-semibold">Text Content</span>
              </div>
            </template>
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-surface-700">Content</label>
              <Textarea
                v-model="editItem.content"
                placeholder="Enter your text content"
                rows="6"
                class="w-full"
              />
            </div>
          </Panel>

          <!-- Image/Map/Diagram Type Content -->
          <Panel v-if="['image', 'map', 'diagram'].includes(editItem.type)" header="File Upload" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-image" size="small" class="bg-indigo-100 text-indigo-600" />
                <span class="font-semibold">File Upload</span>
              </div>
            </template>
            <div class="space-y-4">
              <div class="flex flex-col gap-2">
                <div class="flex items-center gap-4">
                  <RadioButton v-model="imageSourceType" inputId="edit-upload" value="upload" />
                  <label for="edit-upload">Upload File</label>
                  <RadioButton v-model="imageSourceType" inputId="edit-url" value="url" />
                  <label for="edit-url">Use URL</label>
                </div>
              </div>

              <div v-if="imageSourceType === 'upload'">
                <div class="space-y-2">
                  <label class="block text-sm font-semibold text-surface-700">Upload File</label>
                  <FileUpload
                    mode="basic"
                    name="image"
                    accept="image/*"
                    :maxFileSize="10000000" 
                    @select="onFileSelect"
                    :auto="false"
                    :fileLimit="1"
                    :chooseLabel="editItem.imageUrl ? 'Change Image' : 'Choose File'"
                    class="w-full"
                    ref="fileUploadRef"
                  />
                  <div v-if="editItem.imageUrl" class="relative mt-2">
                    <img :src="editItem.imageUrl" :alt="editItem.title" class="w-full h-auto rounded-lg" />
                    <Button
                      icon="pi pi-times"
                      class="p-button-rounded p-button-danger p-button-text absolute top-2 right-2"
                      @click="removeImage"
                      v-tooltip="'Remove Image'"
                    />
                  </div>
                  <small v-if="editItem.imageUrl" class="text-surface-500">Current image will be replaced.</small>
                </div>
              </div>

              <div v-else-if="imageSourceType === 'url'">
                <div class="space-y-2">
                  <label class="block text-sm font-semibold text-surface-700">Image URL</label>
                  <InputText
                    v-model="editItem.url"
                    placeholder="Enter image URL"
                    class="w-full"
                  />
                </div>
              </div>
            </div>
          </Panel>

          <!-- Link Type Content -->
          <Panel v-if="editItem.type === 'link'" header="Link Details" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-link" size="small" class="bg-cyan-100 text-cyan-600" />
                <span class="font-semibold">Link Details</span>
              </div>
            </template>
            <div class="space-y-4">
              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">URL</label>
                <InputText
                  v-model="editItem.url"
                  placeholder="Enter the URL"
                  class="w-full"
                />
              </div>
              
              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Description</label>
                <Textarea
                  v-model="editItem.content"
                  placeholder="Enter a description for this link"
                  rows="3"
                  class="w-full"
                />
              </div>
            </div>
          </Panel>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="Cancel"
          icon="pi pi-times"
          severity="secondary"
          outlined
          @click="handleClose"
        />
        <Button
          label="Save Changes"
          icon="pi pi-check"
          :disabled="!canSave"
          :loading="store.actionLoading.updatingItem"
          @click="saveChanges"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useBookStore } from '../../stores/book';
import type { Item, TestQuestion } from '../../types/item';
import Dialog from 'primevue/dialog';
import Select from 'primevue/select';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import FileUpload from 'primevue/fileupload';
import RadioButton from 'primevue/radiobutton';
import Button from 'primevue/button';
import Panel from 'primevue/panel';
import Avatar from 'primevue/avatar';
import Badge from 'primevue/badge';
import InputNumber from 'primevue/inputnumber';

interface Props {
  visible: boolean;
  item?: Item;
  bookId?: string;
  chapterId?: string;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'itemUpdated', item: Item): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const store = useBookStore();

const editItem = ref<Item>({} as Item);
const selectedFile = ref<File | null>(null);
const fileUploadRef = ref<any>(null);
const imageSourceType = ref<'upload' | 'url'>('upload'); // Default to upload

// Test question navigation
const currentTestQuestionIndex = ref(0);

const currentTestQuestion = computed(() => {
  if (!editItem.value.testQuestions || editItem.value.testQuestions.length === 0) {
    return null;
  }
  return editItem.value.testQuestions[currentTestQuestionIndex.value];
});

const itemTypeOptions = [
  { label: 'Question', value: 'question' },
  { label: 'Test', value: 'test' },
  { label: 'Text Content', value: 'text' },
  { label: 'Image Upload', value: 'image' },
  { label: 'Link Details', value: 'link' },
  { label: 'Map Upload', value: 'map' },
  { label: 'Diagram Upload', value: 'diagram' },
  { label: 'Timed Question', value: 'timed-question' }
];

const dialogTitle = computed(() => {
  return `Edit ${editItem.value.title || 'Item'}`;
});

const canSave = computed(() => {
  return editItem.value.title && editItem.value.title.trim() !== '';
});

// Watch for prop changes to update editItem
watch(() => props.item, (newItem) => {
  if (newItem) {
    editItem.value = { ...newItem };
    // Determine initial image source type
    if (newItem.imageUrl && !newItem.imagePath) {
      imageSourceType.value = 'url';
    } else {
      imageSourceType.value = 'upload';
    }
    // Ensure arrays exist for different item types
    if (editItem.value.type === 'question' && !editItem.value.options) {
      editItem.value.options = [];
    }
    if (editItem.value.type === 'test' && !editItem.value.testQuestions) {
      editItem.value.testQuestions = [];
    }
    // Reset test question navigation
    currentTestQuestionIndex.value = 0;
  }
}, { immediate: true });

watch(imageSourceType, (newType) => {
  if (newType === 'upload') {
    editItem.value.url = ''; // Clear URL if switching to upload
  } else if (newType === 'url') {
    // Clear uploaded image data if switching to URL
    editItem.value.imageUrl = '';
    editItem.value.imagePath = '';
    editItem.value.imageFileName = '';
    editItem.value.imageSize = 0;
    editItem.value.imageMimeType = '';
    selectedFile.value = null;
    if (fileUploadRef.value) {
      fileUploadRef.value.clear();
    }
  }
});

const onFileSelect = (event: any) => {
  selectedFile.value = event.files[0];
};

const removeImage = () => {
  editItem.value.imageUrl = '';
  editItem.value.imagePath = '';
  editItem.value.imageFileName = '';
  selectedFile.value = null;
  if (fileUploadRef.value) {
    fileUploadRef.value.clear();
  }
};

// Question methods
const addOption = () => {
  if (!editItem.value.options) {
    editItem.value.options = [];
  }
  editItem.value.options.push({ text: '' });
};

const removeOption = (index: number) => {
  if (editItem.value.options && editItem.value.options.length > 1) {
    editItem.value.options.splice(index, 1);
    // Adjust correct answer if needed
    if (editItem.value.correctAnswer !== undefined && editItem.value.correctAnswer >= index) {
      if (editItem.value.correctAnswer === index) {
        editItem.value.correctAnswer = undefined;
      } else {
        editItem.value.correctAnswer--;
      }
    }
  }
};

// Test methods
const addTestQuestion = () => {
  if (!editItem.value.testQuestions) {
    editItem.value.testQuestions = [];
  }
  const newQuestion: TestQuestion = {
    id: `q-${Date.now()}`,
    question: '',
    options: [{ text: '' }, { text: '' }],
    correctAnswer: 0
  };
  editItem.value.testQuestions.push(newQuestion);
  // Navigate to the new question
  currentTestQuestionIndex.value = editItem.value.testQuestions.length - 1;
};

const removeTestQuestion = (index: number) => {
  if (editItem.value.testQuestions) {
    editItem.value.testQuestions.splice(index, 1);
    // Adjust current index if needed
    if (currentTestQuestionIndex.value >= editItem.value.testQuestions.length) {
      currentTestQuestionIndex.value = Math.max(0, editItem.value.testQuestions.length - 1);
    }
  }
};

// Test question navigation methods
const nextTestQuestion = () => {
  if (editItem.value.testQuestions && currentTestQuestionIndex.value < editItem.value.testQuestions.length - 1) {
    currentTestQuestionIndex.value++;
  }
};

const previousTestQuestion = () => {
  if (currentTestQuestionIndex.value > 0) {
    currentTestQuestionIndex.value--;
  }
};

const addTestQuestionOption = (questionIndex: number) => {
  if (editItem.value.testQuestions && editItem.value.testQuestions[questionIndex]) {
    editItem.value.testQuestions[questionIndex].options.push({ text: '' });
  }
};

const removeTestQuestionOption = (questionIndex: number, optionIndex: number) => {
  if (editItem.value.testQuestions && editItem.value.testQuestions[questionIndex]) {
    const question = editItem.value.testQuestions[questionIndex];
    if (question.options.length > 2) {
      question.options.splice(optionIndex, 1);
      // Adjust correct answer if needed
      if (question.correctAnswer >= optionIndex) {
        if (question.correctAnswer === optionIndex) {
          question.correctAnswer = 0;
        } else {
          question.correctAnswer--;
        }
      }
    }
  }
};

const saveChanges = async () => {
  if (!props.bookId || !props.chapterId || !editItem.value.id) {
    return;
  }

  try {
    const updatedItem = await store.updateItem(
      props.bookId,
      props.chapterId,
      editItem.value.id,
      editItem.value,
      selectedFile.value
    );

    if (updatedItem) {
      emit('itemUpdated', updatedItem);
      emit('update:visible', false);
    }
  } catch (error) {
    console.error('Error updating item:', error);
  }
};

const handleClose = () => {
  emit('update:visible', false);
};
</script>

<style scoped>
.item-edit-dialog :deep(.p-dialog) {
  border-radius: 12px;
}

.item-edit-dialog :deep(.p-dialog-header) {
  border-radius: 12px 12px 0 0;
}
</style>
