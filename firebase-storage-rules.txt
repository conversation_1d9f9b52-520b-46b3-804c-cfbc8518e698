// Firebase Storage Security Rules for Textbook Platform
// Copy these rules to your Firebase Storage Rules in the Firebase Console

rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Helper function to check if user has editor role or higher
    function hasEditPermission() {
      return isAuthenticated() && 
             request.auth.token.role in ['editor', 'admin', 'super-admin'];
    }
    
    // Helper function to check if user has viewer role or higher
    function hasViewPermission() {
      return isAuthenticated() && 
             request.auth.token.role in ['viewer', 'editor', 'admin', 'super-admin'];
    }
    
    // Helper function to validate image file types
    function isValidImageType() {
      return request.resource.contentType.matches('image/.*');
    }
    
    // Helper function to validate file size (10MB limit)
    function isValidFileSize() {
      return request.resource.size <= 10 * 1024 * 1024; // 10MB
    }
    
    // Rules for book images: images/{bookId}/{chapterId}/{fileName}
    // Allow public read access for images displayed via the /item route
    match /images/{bookId}/{chapterId}/{fileName} {
      allow read;
      
      // Allow write (create/update) access for authenticated users with editor role or higher
      // Only allow image files under 10MB
      allow write: if hasEditPermission() && 
                      isValidImageType() && 
                      isValidFileSize();
      
      // Allow delete access for authenticated users with editor role or higher
      allow delete: if hasEditPermission();
    }
    
    // Deny all other access patterns
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}

// Instructions for applying these rules:
// 1. Go to Firebase Console > Storage > Rules
// 2. Replace the existing rules with the rules above
// 3. Click "Publish" to apply the changes
//
// These rules ensure:
// - Only authenticated users can access files
// - Only users with editor/admin roles can upload/modify images
// - All users with viewer+ roles can view images
// - Only image files are allowed
// - File size is limited to 10MB
// - Files are organized by book/chapter structure
// - All other access is denied for security
      
      // Allow write (create/update) access for authenticated users with editor role or higher
      // Only allow image files under 10MB
      allow write: if hasEditPermission() && 
                      isValidImageType() && 
                      isValidFileSize();
      
      // Allow delete access for authenticated users with editor role or higher
      allow delete: if hasEditPermission();
    }
    
    // Deny all other access patterns
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}

// Instructions for applying these rules:
// 1. Go to Firebase Console > Storage > Rules
// 2. Replace the existing rules with the rules above
// 3. Click "Publish" to apply the changes
//
// These rules ensure:
// - Only authenticated users can access files
// - Only users with editor/admin roles can upload/modify images
// - All users with viewer+ roles can view images
// - Only image files are allowed
// - File size is limited to 10MB
// - Files are organized by book/chapter structure
// - All other access is denied for security
