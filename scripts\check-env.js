#!/usr/bin/env node

/**
 * Environment Configuration Checker
 * This script helps verify that the environment variables are properly configured
 */

console.log('🔍 Environment Configuration Check\n');

// Simulate Vite environment loading
const mode = process.argv[2] || 'development';
console.log(`Mode: ${mode}\n`);

// Check which .env files would be loaded
const envFiles = [
  '.env',
  `.env.${mode}`,
  '.env.local',
  `.env.${mode}.local`
];

console.log('📁 Environment files that would be loaded (in order):');
envFiles.forEach((file, index) => {
  console.log(`  ${index + 1}. ${file}`);
});

console.log('\n🔧 Actual files for this project:');
console.log('  • .env (production default)');
console.log('  • .env.development.local (local development)');
console.log('  • .env.production (production builds)');

console.log('\n📋 Expected configuration:');
console.log('┌─────────────────────┬─────────────────────────────────────────────────────────┐');
console.log('│ Command             │ API Base URL                                            │');
console.log('├─────────────────────┼─────────────────────────────────────────────────────────┤');
console.log('│ npm run dev         │ https://my-textbook-app-376480732351.us-central1.run.app │');
console.log('│ npm run dev:local   │ http://localhost:8080                                   │');
console.log('│ npm run build       │ https://my-textbook-app-376480732351.us-central1.run.app │');
console.log('└─────────────────────┴─────────────────────────────────────────────────────────┘');

console.log('\n✅ Setup complete! Use the appropriate npm script for your needs:');
console.log('   • npm run dev:local  - For local development with local server');
console.log('   • npm run dev        - For testing with live server');
console.log('   • npm run build      - For production build (Netlify deployment)');
