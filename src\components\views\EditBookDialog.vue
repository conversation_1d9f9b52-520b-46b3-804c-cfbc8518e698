<template>
  <Dialog
    v-model:visible="showDialog"
    header="Edit Book Details & Theme"
    :modal="true"
    :style="{ width: '40rem' }"
    @update:visible="closeDialog"
  >
    <div v-if="editableBook" class="p-4">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Book Details -->
        <div class="space-y-4">
          <h3 class="font-semibold text-lg text-surface-800 border-b pb-2 mb-4">Book Details</h3>
          <div class="flex flex-col">
            <label for="title" class="font-medium text-sm text-surface-600 mb-1">Title</label>
            <InputText id="title" v-model="editableBook.title" class="w-full" />
          </div>
          <div class="flex flex-col">
            <label for="description" class="font-medium text-sm text-surface-600 mb-1">Description</label>
            <Textarea id="description" v-model="editableBook.description" rows="3" class="w-full" />
          </div>
        </div>

        <!-- Book Theme -->
        <div class="space-y-4">
          <h3 class="font-semibold text-lg text-surface-800 border-b pb-2 mb-4">Book Theme</h3>
          <div class="grid grid-cols-2 gap-4">
            <div v-for="key in themeKeys" :key="key" class="flex flex-col">
              <label :for="key" class="font-medium text-sm text-surface-600 mb-1">{{ formatLabel(key) }}</label>
              <div class="relative">
                <ColorPicker :id="key" v-model="(editableBook.theme as any)[key]" class="w-full" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <Button label="Cancel" severity="secondary" @click="closeDialog" class="transition-all duration-200" />
      <Button label="Save Changes" icon="pi pi-check" @click="saveBook" class="transition-all duration-200" />
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import Dialog from 'primevue/dialog';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import ColorPicker from 'primevue/colorpicker';
import { useBookStore } from '../../stores/book';
import type { Book } from '../../types/book';

const props = defineProps<{ visible: boolean; book: Book | null }>();
const emits = defineEmits(['update:visible', 'bookUpdated']);

const store = useBookStore();
const showDialog = ref(props.visible);
const editableBook = ref<Book | null>(null);

const defaultTheme = {
  primaryColor: '#10B981',
  secondaryColor: '#6366F1',
  accentColor: '#F59E0B',
};

const themeKeys = ['primaryColor', 'secondaryColor', 'accentColor'];

watch(() => props.visible, (newValue) => {
  showDialog.value = newValue;
  if (newValue && props.book) {
    editableBook.value = JSON.parse(JSON.stringify(props.book)); // Deep clone
    if (editableBook.value) {
      // Ensure theme exists and has all default keys
      editableBook.value.theme = editableBook.value.theme || {};
      for (const key of themeKeys) {
        if (!(editableBook.value.theme as any)[key]) {
          (editableBook.value.theme as any)[key] = (defaultTheme as any)[key];
        }
      }
    }
  } else {
    editableBook.value = null;
  }
});

const closeDialog = () => {
  emits('update:visible', false);
};

const saveBook = async () => {
  if (!editableBook.value) return;
  const { id, title, description } = editableBook.value;
  const themeToSend: Record<string, string> = {};
  if (editableBook.value.theme) {
    for (const key of themeKeys) {
      const color = (editableBook.value.theme as any)[key];
      themeToSend[key] = color.startsWith('#') ? color : `#${color}`;
    }
  }

  if (id) {
    await store.updateBook(id, { title, description, theme: themeToSend });
    emits('bookUpdated');
    closeDialog();
  }
};

const formatLabel = (key: string) => {
  return key.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase());
};
</script>
