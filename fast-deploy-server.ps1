# Fast server-only deployment script
Write-Host "Starting fast server-only deployment..." -ForegroundColor Green

# Backup original Dockerfile and use server-only version
Write-Host "Preparing server-only Dockerfile..." -ForegroundColor Yellow
if (Test-Path "Dockerfile") {
    Move-Item "Dockerfile" "Dockerfile.backup"
}
Copy-Item "Dockerfile.server" "Dockerfile"

# Deploy using the server-only Dockerfile
Write-Host "Deploying server with minimal dependencies..." -ForegroundColor Yellow
gcloud run deploy my-textbook-app `
  --source . `
  --region us-central1 `
  --allow-unauthenticated `
  --port 8080

# Restore original Dockerfile
Write-Host "Restoring original Dockerfile..." -ForegroundColor Yellow
Remove-Item "Dockerfile"
if (Test-Path "Dockerfile.backup") {
    Move-Item "Dockerfile.backup" "Dockerfile"
}

if ($LASTEXITCODE -eq 0) {
    Write-Host "Server deployment completed successfully!" -ForegroundColor Green
    Write-Host "Server URL: https://my-textbook-app-376480732351.us-central1.run.app" -ForegroundColor Cyan
} else {
    Write-Host "Deployment failed!" -ForegroundColor Red
    exit 1
}
