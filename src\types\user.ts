export type UserRole = 'viewer' | 'editor' | 'admin' | 'super-admin';

export interface User {
  id: string;
  username: string;
  email: string;
  role: UserR<PERSON>;
  createdAt: number;
  lastLogin?: number;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  token: string;
}

export interface Permission {
  canView: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canManageUsers: boolean;
  canCreateBooks: boolean;
  canEditBooks: boolean;
  canDeleteBooks: boolean;
  canCreateChapters: boolean;
  canDeleteChapters: boolean;
}

export const getRolePermissions = (role: UserRole): Permission => {
  switch (role) {
    case 'viewer':
      return {
        canView: true,
        canEdit: false,
        canDelete: false,
        canManageUsers: false,
        canCreateBooks: false,
        canEditBooks: false,
        canDeleteBooks: false,
        canCreateChapters: false,
        canDeleteChapters: false,
      };
    case 'editor':
      return {
        canView: true,
        canEdit: true,
        canDelete: false,
        canManageUsers: false,
        canCreateBooks: false,
        canEditBooks: false,
        canDeleteBooks: false,
        canCreateChapters: false,
        canDeleteChapters: false,
      };
    case 'admin':
      return {
        canView: true,
        canEdit: true,
        canDelete: true,
        canManageUsers: false, // Content admin - no user management
        canCreateBooks: true,
        canEditBooks: true,
        canDeleteBooks: true,
        canCreateChapters: true,
        canDeleteChapters: true,
      };
    case 'super-admin':
      return {
        canView: true,
        canEdit: true,
        canDelete: true,
        canManageUsers: true, // Only super-admin can manage users
        canCreateBooks: true,
        canEditBooks: true,
        canDeleteBooks: true,
        canCreateChapters: true,
        canDeleteChapters: true,
      };
    default:
      return {
        canView: false,
        canEdit: false,
        canDelete: false,
        canManageUsers: false,
        canCreateBooks: false,
        canEditBooks: false,
        canDeleteBooks: false,
        canCreateChapters: false,
        canDeleteChapters: false,
      };
  }
};